// uniCloud云函数：基于GPT的高质量字幕翻译 - 纯并发处理版本
"use strict";

const createConfig = require("uni-config-center");

// 创建语言配置实例
const languageConfig = createConfig({
  pluginId: "subtitle-language",
});

// 常量配置
const CONFIG = {
  DEFAULT_MODEL: "gpt-5-mini",
  TEMPERATURE: 0.3,
  MAX_TOKENS: 4000,
  API_TIMEOUT: 60000 * 2,
  BATCH_SIZE: 50,
  MAX_RETRIES: 3,
  MAX_CONCURRENT_BATCHES: 10,
};

// ASS字幕样式配置现在通过 uni-config-center/subtitle-language 模块管理

/**
 * 标准化语言标识符
 * @param {string} languageIdentifier - 原始语言标识符
 * @returns {string} 标准化后的语言代码
 */
function normalizeLanguageIdentifier(languageIdentifier) {
  if (!languageIdentifier) return "en"; // 默认英文

  const languageMap = languageConfig.config("languageMap") || {};
  const identifierMap = languageConfig.config("languageIdentifierMap") || {};

  // 直接匹配标准代码
  if (languageMap[languageIdentifier]) {
    return languageIdentifier;
  }

  // 通过映射表转换
  const normalizedCode = identifierMap[languageIdentifier];
  if (normalizedCode) {
    return normalizedCode;
  }

  // 尝试小写匹配
  const lowerCase = languageIdentifier.toLowerCase();
  const lowerCaseMatch = identifierMap[lowerCase];
  if (lowerCaseMatch) {
    return lowerCaseMatch;
  }

  console.warn(`未知的语言标识符: ${languageIdentifier}，使用默认值 en`);
  return "en"; // 默认返回英文
}

/**
 * 并发翻译字幕条目 - 使用Promise.all
 */
async function translateSubtitlesBatchOptimized(
  entries,
  apiKey,
  baseUrl,
  model,
  sourceLanguage,
  targetLanguage
) {
  const translationStartTime = Date.now();
  console.log("开始并发翻译字幕", {
    totalEntries: entries.length,
    batchSize: CONFIG.BATCH_SIZE,
    maxConcurrent: CONFIG.MAX_CONCURRENT_BATCHES,
    sourceLanguage,
    targetLanguage,
    model,
  });

  // 过滤有效文本条目
  const validEntries = [];
  entries.forEach((entry, index) => {
    if (entry.text?.trim()) {
      validEntries.push({ ...entry, originalIndex: index });
    }
  });

  if (validEntries.length === 0) {
    console.log("没有有效的字幕文本需要翻译");
    return entries;
  }

  console.log(`有效字幕条目: ${validEntries.length}/${entries.length}`);

  // 将有效条目分批处理
  const batches = [];
  for (let i = 0; i < validEntries.length; i += CONFIG.BATCH_SIZE) {
    batches.push(validEntries.slice(i, i + CONFIG.BATCH_SIZE));
  }

  console.log(`分为 ${batches.length} 批处理，每批最多 ${CONFIG.BATCH_SIZE} 条`);

  // 复制原数组用于存储翻译结果
  const translatedEntries = [...entries];

  // 纯并发处理
  console.log(`启用纯并发处理，最大并发数: ${CONFIG.MAX_CONCURRENT_BATCHES}`);
  const totalTranslatedCount = await processBatchesConcurrently(
    batches,
    translatedEntries,
    apiKey,
    baseUrl,
    model,
    targetLanguage
  );

  const totalTime = (Date.now() - translationStartTime) / 1000;
  console.log(`并发翻译完成`, {
    totalEntries: entries.length,
    validEntries: validEntries.length,
    translatedCount: totalTranslatedCount,
    batchCount: batches.length,
    processingTime: `${totalTime.toFixed(2)}秒`,
    successRate: `${((totalTranslatedCount / validEntries.length) * 100).toFixed(1)}%`,
  });

  return translatedEntries;
}

/**
 * 并发处理多个批次 - 使用Promise.all
 */
async function processBatchesConcurrently(
  batches,
  translatedEntries,
  apiKey,
  baseUrl,
  model,
  targetLanguage
) {
  let totalTranslatedCount = 0;
  const maxConcurrent = Math.min(CONFIG.MAX_CONCURRENT_BATCHES, batches.length);

  // 将批次分组，每组最多包含 maxConcurrent 个批次
  for (let i = 0; i < batches.length; i += maxConcurrent) {
    const batchGroup = batches.slice(i, i + maxConcurrent);
    const groupStartTime = Date.now();

    console.log(
      `并发处理第 ${i + 1}-${Math.min(i + maxConcurrent, batches.length)} 批次（共 ${
        batchGroup.length
      } 个并发）`
    );

    // 创建并发任务 - 使用Promise.all
    const concurrentTasks = batchGroup.map(async (batch, groupIndex) => {
      const actualBatchIndex = i + groupIndex + 1;
      try {
        const batchResult = await translateBatchWithRetry(
          batch,
          apiKey,
          baseUrl,
          model,
          targetLanguage,
          actualBatchIndex
        );
        return { success: true, batchResult, batch, batchIndex: actualBatchIndex };
      } catch (error) {
        return { success: false, error, batch, batchIndex: actualBatchIndex };
      }
    });

    // 等待当前组的所有批次完成
    const results = await Promise.all(concurrentTasks);

    // 处理结果
    results.forEach(({ success, batchResult, batch, batchIndex, error }) => {
      if (success) {
        // 将翻译结果合并到最终数组中
        batchResult.forEach((translatedEntry) => {
          if (translatedEntry.originalIndex !== undefined) {
            translatedEntries[translatedEntry.originalIndex] = translatedEntry;
            totalTranslatedCount++;
          }
        });
        console.log(`第 ${batchIndex} 批翻译完成`);
      } else {
        console.error(`第 ${batchIndex} 批翻译失败:`, error.message);
        // 批次失败时保留原文
        batch.forEach((entry) => {
          if (entry.originalIndex !== undefined) {
            translatedEntries[entry.originalIndex] = entry;
          }
        });
      }
    });

    const groupTime = (Date.now() - groupStartTime) / 1000;
    console.log(`并发组处理完成，耗时: ${groupTime.toFixed(2)}秒`);
  }

  return totalTranslatedCount;
}

/**
 * 带重试机制的单批次翻译函数
 */
async function translateBatchWithRetry(batch, apiKey, baseUrl, model, targetLanguage, batchNumber) {
  let lastError = null;

  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试翻译...`);

      const result = await translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage);

      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试成功`);
      return result;
    } catch (error) {
      lastError = error;
      console.error(`第 ${batchNumber} 批第 ${attempt} 次尝试失败:`, error.message);

      if (attempt < CONFIG.MAX_RETRIES) {
        console.log(`立即重试第 ${attempt + 1} 次...`);
      }
    }
  }

  // 所有重试都失败，抛出最后一个错误
  throw new Error(
    `第 ${batchNumber} 批翻译失败，已重试 ${CONFIG.MAX_RETRIES} 次: ${lastError.message}`
  );
}

/**
 * 翻译单个批次的字幕条目
 */
async function translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage) {
  // 构建批次文本
  const batchTexts = batch.map((entry, index) => `${index + 1}. ${entry.text.trim()}`);
  const combinedText = batchTexts.join("\n");

  console.log(`批次文本长度: ${combinedText.length} 字符，包含 ${batch.length} 条字幕`);

  // 获取目标语言名称
  const languageMap = languageConfig.config("languageMap") || {};
  const targetLangName = languageMap[targetLanguage] || targetLanguage.toUpperCase();

  // 构建翻译请求
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一位资深的多语言翻译专家，将带编号文本翻译成地道流畅的${targetLangName}。要求：保持编号格式，逐行对应翻译，忠实原意，自然表达。仅输出译文。`,
      },
      {
        role: "user",
        content: `翻译成${targetLangName}：\n\n${combinedText}`,
      },
    ],
    temperature: CONFIG.TEMPERATURE,
    max_completion_tokens: CONFIG.MAX_TOKENS,
  };

  // 调用GPT API
  const apiStartTime = Date.now();
  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    data: JSON.stringify(requestBody),
    dataType: "json",
    timeout: CONFIG.API_TIMEOUT,
  });

  const apiTime = (Date.now() - apiStartTime) / 1000;
  console.log(`API响应耗时: ${apiTime.toFixed(2)}秒`);

  if (response.status !== 200) {
    let errorMessage = `GPT API请求失败，状态码: ${response.status}`;
    if (response.data?.error?.message) {
      errorMessage += ` - ${response.data.error.message}`;
    }
    throw new Error(errorMessage);
  }

  const result = response.data;
  if (!result.choices?.length || !result.choices[0].message?.content) {
    throw new Error("GPT API返回空结果或格式错误");
  }

  const translatedText = result.choices[0].message.content.trim();
  if (!translatedText) {
    throw new Error("GPT翻译返回空白内容");
  }

  // 解析翻译结果
  const translatedLines = translatedText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line);

  console.log(`解析翻译结果，共 ${translatedLines.length} 行`);

  // 匹配翻译结果到原始条目
  const translatedBatch = [...batch];
  batch.forEach((entry, index) => {
    const targetPattern = `${index + 1}.`;
    let translatedLine = null;

    // 查找对应的翻译
    for (const line of translatedLines) {
      if (line.startsWith(targetPattern)) {
        translatedLine = line.substring(targetPattern.length).trim();
        break;
      }
    }

    if (translatedLine) {
      translatedBatch[index] = {
        ...entry,
        text: translatedLine,
      };
    }
    // 如果没找到翻译，保留原文（translatedBatch[index] 已经是原始条目）
  });

  return translatedBatch;
}

// 辅助函数
function createSuccessResponse(message, data) {
  return { code: 200, message, data, timestamp: new Date().toISOString() };
}

function createErrorResponse(code, message, extra = {}) {
  return { code, message, timestamp: new Date().toISOString(), ...extra };
}

async function validateAndGetTask(tasksCollection, taskId, sourceLanguage) {
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data?.length) {
    throw new Error("任务不存在");
  }

  const task = taskInfo.data[0];
  if (!task.subtitleOssUrl) {
    throw new Error("缺少字幕文件地址");
  }

  // 标准化语言标识符
  const rawSourceLanguage =
    sourceLanguage === "auto" ? task.detectedLanguage || "en" : sourceLanguage;
  const actualSourceLanguage = normalizeLanguageIdentifier(rawSourceLanguage);

  console.log(`语言标识符标准化: ${rawSourceLanguage} -> ${actualSourceLanguage}`);
  return { task, actualSourceLanguage };
}

async function getAndValidateGptConfig() {
  const gptConfig = createConfig({
    pluginId: "openai-api",
    defaultConfig: {
      baseUrl: "https://aihubmix.com",
      model: CONFIG.DEFAULT_MODEL,
    },
  });

  const apiKey = gptConfig.config("apiKey");
  const baseUrl = gptConfig.config("baseUrl");

  if (!apiKey) {
    throw new Error("GPT API配置缺失，请检查apiKey");
  }

  return { apiKey, baseUrl };
}

async function downloadSrtFromOSS(ossUrl) {
  if (!ossUrl || typeof ossUrl !== "string") {
    throw new Error(`无效的OSS URL: ${ossUrl}`);
  }

  const response = await uniCloud.httpclient.request(ossUrl, {
    method: "GET",
    timeout: 30000,
  });

  if (response.status !== 200) {
    throw new Error(`下载SRT文件失败，状态码: ${response.status}`);
  }

  let srtContent = response.data;
  if (Buffer.isBuffer(srtContent)) {
    srtContent = srtContent.toString("utf8");
  } else if (typeof srtContent !== "string") {
    srtContent = String(srtContent || "");
  }

  if (!srtContent.trim()) {
    throw new Error("下载的SRT文件内容为空");
  }

  return srtContent;
}

function parseSRT(srtContent) {
  if (!srtContent || !srtContent.trim()) {
    throw new Error("SRT字幕内容为空");
  }

  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    if (!block?.trim()) continue;

    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n").trim();

      if (!isNaN(index) && timeRange && text) {
        entries.push({ index, timeRange, text });
      }
    }
  }

  console.log(`SRT解析完成，共 ${entries.length} 条有效字幕`);
  return entries;
}

/**
 * 生成ASS字幕格式（完全复用现有entries结构）
 * @param {Array} entries - 复用parseSRT()返回的字幕条目数组
 * @param {string} targetLanguage - 复用现有目标语言参数
 * @param {Object} styleOverrides - 可选的样式覆盖配置
 * @param {Object} videoResolution - 视频分辨率信息 {width, height}
 * @returns {string} ASS格式字符串
 */
function generateASS(entries, targetLanguage = "zh", styleOverrides = {}, videoResolution = null) {
  const encoding = getLanguageEncoding(targetLanguage);

  // 获取ASS样式配置
  const assStyleConfig = languageConfig.config("assStyleConfig") || {};
  const baseStyle = assStyleConfig.baseStyle || {};
  const scriptInfo = assStyleConfig.scriptInfo || {};

  // 获取语言特定的 WrapStyle 配置
  const languageWrapStyles = assStyleConfig.languageWrapStyles || {};
  const wrapStyle = languageWrapStyles[targetLanguage] || languageWrapStyles.default || scriptInfo.wrapStyle || 1;

  console.log(`ASS 字幕配置: 语言=${targetLanguage}, WrapStyle=${wrapStyle} (${getWrapStyleDescription(wrapStyle)})`);

  // 合并默认配置和覆盖配置
  const style = { ...baseStyle, ...styleOverrides };

  // 生成分辨率配置 - 优先使用实际视频分辨率
  let resolutionConfig = "";
  if (videoResolution && videoResolution.width && videoResolution.height) {
    resolutionConfig = `PlayResX: ${videoResolution.width}\nPlayResY: ${videoResolution.height}`;
    console.log(`ASS 使用实际视频分辨率: ${videoResolution.width}x${videoResolution.height}`);
  } else if (!scriptInfo.autoAdaptive) {
    resolutionConfig = `PlayResX: 1920\nPlayResY: 1080`;
    console.log("ASS 使用默认分辨率: 1920x1080");
  } else {
    console.log("ASS 使用自适应分辨率模式");
  }
 
  const assHeader = `[Script Info]
Title: ${scriptInfo.title}
ScriptType: ${scriptInfo.scriptType}
WrapStyle: ${wrapStyle}
ScaledBorderAndShadow: ${scriptInfo.scaledBorderAndShadow}
${resolutionConfig}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${style.fontName},${style.fontSize},${style.primaryColor},${style.secondaryColor},${style.outlineColor},${style.backColor},${style.bold},${style.italic},${style.underline},${style.strikeOut},${style.scaleX},${style.scaleY},${style.spacing},${style.angle},${style.borderStyle},${style.outline},${style.shadow},${style.alignment},${style.marginL},${style.marginR},${style.marginV},${encoding}`;

  const assEvents = entries
    .map((entry, index) => {
      const { startTime, endTime } = convertTimeRange(entry.timeRange);
      // 文本预处理：仅处理换行符，ASS 将使用 WrapStyle 进行自动换行
      const processedText = smartTextWrap(entry.text, targetLanguage);
      return `Dialogue: ${index},${startTime},${endTime},Default,,0,0,0,,${processedText}`;
    })
    .join("\n");

  return `${assHeader}\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n${assEvents}`;
}

/**
 * 转换时间格式（复用现有timeRange格式）
 * @param {string} timeRange - SRT格式的时间范围
 * @returns {Object} ASS格式的开始和结束时间
 */
function convertTimeRange(timeRange) {
  const [start, end] = timeRange.split(" --> ");
  return {
    startTime: convertSRTTimeToASS(start),
    endTime: convertSRTTimeToASS(end),
  };
}

/**
 * 将SRT时间格式转换为ASS时间格式
 * @param {string} srtTime - SRT时间格式 (HH:MM:SS,mmm)
 * @returns {string} ASS时间格式 (H:MM:SS.cc)
 */
function convertSRTTimeToASS(srtTime) {
  const [time, milliseconds] = srtTime.split(",");
  const centiseconds = Math.floor(parseInt(milliseconds) / 10);
  const [hours, minutes, seconds] = time.split(":");
  return `${parseInt(hours)}:${minutes}:${seconds}.${centiseconds.toString().padStart(2, "0")}`;
}

/**
 * 获取语言编码（简化版）
 * @param {string} targetLanguage - 目标语言代码
 * @returns {number} 对应的字符编码
 */
function getLanguageEncoding(targetLanguage) {
  const encodings = languageConfig.config("languageEncodings") || {};
  return encodings[targetLanguage] || 1;
}

/**
 * 获取语言特定字体
 * @param {string} targetLanguage - 目标语言代码
 * @returns {string} 对应的字体名称
 */
function getLanguageFont(targetLanguage) {
  const fonts = languageConfig.config("languageFonts") || { default: "Arial" };
  return fonts[targetLanguage] || fonts.default || "Arial";
}

/**
 * 获取 WrapStyle 的描述信息
 * @param {number} wrapStyle - WrapStyle 值
 * @returns {string} 描述信息
 */
function getWrapStyleDescription(wrapStyle) {
  const descriptions = {
    0: "智能换行，行会被均匀分割",
    1: "行尾单词换行，只有\\N会强制换行",
    2: "不进行单词换行，宽行会延伸到屏幕边缘",
    3: "智能换行，类似样式0但底行更宽"
  };
  return descriptions[wrapStyle] || "未知样式";
}

/**
 * 字体大小合理性验证
 * @param {number} fontSize - 字体大小
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @param {string} targetLanguage - 目标语言
 * @returns {Object} 验证结果和建议
 */
function validateFontSize(fontSize, videoWidth, videoHeight, targetLanguage) {
  const minDimension = Math.min(videoWidth || 1080, videoHeight || 1080);
  const ratio = fontSize / minDimension;

  // 定义合理的字体大小比例范围
  const ratioRanges = {
    min: 0.02,  // 最小比例 2%
    ideal: 0.04, // 理想比例 4%
    max: 0.08   // 最大比例 8%
  };

  let status = "good";
  let suggestions = [];

  if (ratio < ratioRanges.min) {
    status = "too_small";
    suggestions.push(`字体过小，建议至少 ${Math.round(minDimension * ratioRanges.min)}px`);
  } else if (ratio > ratioRanges.max) {
    status = "too_large";
    suggestions.push(`字体过大，建议不超过 ${Math.round(minDimension * ratioRanges.max)}px`);
  } else if (Math.abs(ratio - ratioRanges.ideal) < 0.01) {
    status = "ideal";
    suggestions.push("字体大小接近理想比例");
  }

  // 语言特定建议
  const languageRecommendations = {
    "zh": "中文字符建议使用稍大字体以提高可读性",
    "ja": "日文假名建议使用适中字体大小",
    "ko": "韩文字符建议保持标准字体大小",
    "ar": "阿拉伯文建议使用较大字体以适应从右到左阅读",
    "en": "英文字母可以使用相对较小的字体"
  };

  if (languageRecommendations[targetLanguage]) {
    suggestions.push(languageRecommendations[targetLanguage]);
  }

  console.log(`
📏 字体大小验证:
  - 字体大小: ${fontSize}px
  - 视频尺寸: ${videoWidth}×${videoHeight}
  - 字体比例: ${(ratio * 100).toFixed(2)}%
  - 验证状态: ${status}
  - 建议: ${suggestions.join("; ")}
  `);

  return {
    status,
    ratio,
    suggestions,
    isValid: status === "good" || status === "ideal"
  };
}

/**
 * 文本预处理 - 仅处理换行符，不进行手动换行
 * @param {string} text - 原始文本
 * @param {string} _targetLanguage - 目标语言（保留参数以兼容现有调用，但不再使用）
 * @returns {string} 处理后的文本
 */
function smartTextWrap(text, _targetLanguage = "zh") {
  if (!text || !text.trim()) return text;

  // 仅处理已有的换行符，将其转换为空格
  // ASS 格式将使用原生的 WrapStyle 进行自动换行
  return text.replace(/\r?\n/g, " ");
}



/**
 * 获取视频分辨率信息（从数据库读取或使用默认值）
 * @param {string} taskId - 任务ID（用于查询视频信息）
 * @param {Object} tasksCollection - 数据库任务集合
 * @returns {Promise<Object>} 包含视频宽度和高度的对象
 */
async function getVideoResolution(taskId, tasksCollection) {
  try {
    if (taskId && tasksCollection) {
      // 从数据库查询视频分辨率信息
      const taskResult = await tasksCollection.doc(taskId).get();
      if (taskResult.data && taskResult.data.length > 0) {
        const task = taskResult.data[0];
        if (task.videoWidth && task.videoHeight) {
          console.log(`📹 从数据库获取视频分辨率: ${task.videoWidth}x${task.videoHeight}`);
          console.log(
            `📊 视频类型判断: ${
              task.videoHeight > task.videoWidth
                ? "竖屏"
                : task.videoWidth === task.videoHeight
                ? "正方形"
                : "横屏"
            }`
          );
          console.log(
            `🎯 匹配维度: ${Math.min(task.videoWidth, task.videoHeight)}px (用于分辨率等级判断)`
          );
          return {
            width: task.videoWidth,
            height: task.videoHeight,
            source: "database",
          };
        }
      }
    }
  } catch (error) {
    console.warn("从数据库获取视频分辨率失败:", error.message);
  }

  // 返回常见的1080p作为默认值
  console.log("使用默认视频分辨率: 1920x1080");
  return {
    width: 1920,
    height: 1080,
    source: "default", // 标记这是默认值而非实际检测值
  };
}

/**
 * 获取分辨率等级
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {string} 分辨率等级
 */
function getResolutionLevel(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) {
    console.log(`⚠️  视频分辨率信息缺失，使用默认分辨率等级: 1080p`);
    return "1080p";
  }

  const minDimension = Math.min(videoWidth, videoHeight);
  let resolutionLevel;

  if (minDimension <= 480) resolutionLevel = "480p";
  else if (minDimension <= 720) resolutionLevel = "720p";
  else if (minDimension <= 1080) resolutionLevel = "1080p";
  else if (minDimension <= 1440) resolutionLevel = "1440p";
  else if (minDimension <= 2160) resolutionLevel = "4k";
  else resolutionLevel = "8k";

  console.log(
    `🎯 分辨率等级判断: ${videoWidth}x${videoHeight} → 最小维度${minDimension}px → ${resolutionLevel}`
  );
  return resolutionLevel;
}

/**
 * 获取视频类型
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {string} 视频类型
 */
function getVideoType(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) return "horizontal";

  if (videoHeight > videoWidth) return "vertical";
  if (videoWidth === videoHeight) return "square";
  return "horizontal";
}

/**
 * 智能字体大小计算器 - 基于视频分辨率和语言特性
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @param {string} targetLanguage - 目标语言代码
 * @param {string} videoType - 视频类型 (horizontal/vertical/square)
 * @returns {number} 计算出的字体大小
 */
function calculateSmartFontSize(videoWidth, videoHeight, targetLanguage, videoType) {
  const assStyleConfig = languageConfig.config("assStyleConfig") || {};
  const fontSizeCalc = assStyleConfig.fontSizeCalculation || {};
  const languageAdjustments = assStyleConfig.languageAdjustments || {};
  const videoTypeOptimization = assStyleConfig.videoTypeOptimization || {};

  // 获取基础参数
  const minDimension = Math.min(videoWidth || 1080, videoHeight || 1080);
  const baseRatio = fontSizeCalc.rules?.baseRatio || 0.04;
  const minFontSize = fontSizeCalc.rules?.minFontSize || 12;
  const maxFontSize = fontSizeCalc.rules?.maxFontSize || 120;

  // 基础字体大小 = 较小维度 × 基础比例
  let fontSize = Math.round(minDimension * baseRatio);

  // 应用视频类型缩放
  const typeConfig = videoTypeOptimization[videoType] || { fontScale: 1.0 };
  fontSize = Math.round(fontSize * typeConfig.fontScale);

  // 应用语言特定缩放
  const langConfig = languageAdjustments[targetLanguage] || { fontScale: 1.0 };
  fontSize = Math.round(fontSize * (langConfig.fontScale || 1.0));

  // 应用质量因子（针对低分辨率视频的补偿）
  const resolutionLevel = getResolutionLevel(videoWidth, videoHeight);
  const qualityFactors = fontSizeCalc.qualityFactors || {};
  const qualityFactor = qualityFactors[resolutionLevel] || 1.0;
  fontSize = Math.round(fontSize * qualityFactor);

  // 限制在合理范围内
  fontSize = Math.max(minFontSize, Math.min(maxFontSize, fontSize));

  console.log(`
🧮 智能字体大小计算:
  - 视频尺寸: ${videoWidth}×${videoHeight}
  - 较小维度: ${minDimension}px
  - 基础比例: ${baseRatio}
  - 基础字体: ${Math.round(minDimension * baseRatio)}px
  - 视频类型缩放: ×${typeConfig.fontScale} (${videoType})
  - 语言缩放: ×${langConfig.fontScale || 1.0} (${targetLanguage})
  - 质量补偿: ×${qualityFactor} (${resolutionLevel})
  - 最终字体大小: ${fontSize}px
  `);

  return fontSize;
}

/**
 * 统一的字幕样式生成器 - 整合所有样式逻辑
 * @param {string} targetLanguage - 目标语言代码
 * @param {number} videoWidth - 视频宽度（可选）
 * @param {number} videoHeight - 视频高度（可选）
 * @returns {Object} 完整的字幕样式配置
 */
function getLanguageSpecificStyle(targetLanguage, videoWidth = null, videoHeight = null) {
  // 获取基础配置
  const resolutionLevel = getResolutionLevel(videoWidth, videoHeight);
  const videoType = getVideoType(videoWidth, videoHeight);

  // 获取ASS样式配置
  const assStyleConfig = languageConfig.config("assStyleConfig") || {};
  const resolutionLevels = assStyleConfig.resolutionLevels || {};
  const videoTypeOptimization = assStyleConfig.videoTypeOptimization || {};
  const languageAdjustments = assStyleConfig.languageAdjustments || {};

  // 获取配置对象
  const resConfig = resolutionLevels[resolutionLevel] || { baseSize: 32, marginBase: 40 };
  const typeConfig = videoTypeOptimization[videoType] || {
    fontScale: 1.0,
    marginScale: 1.0,
    sideMargin: 30,
  };
  const langConfig = languageAdjustments[targetLanguage] || {
    marginExtra: 0,
    verticalExtra: 0,
    fontScale: 1.0
  };

  // 获取语言特定字体
  const languageFont = getLanguageFont(targetLanguage);

  console.log(
    `字幕样式配置: ${resolutionLevel} ${videoType}视频, 语言: ${targetLanguage}, 字体: ${
      languageFont.split(",")[0]
    }`
  );

  // 使用智能字体大小计算
  const fontSize = calculateSmartFontSize(videoWidth, videoHeight, targetLanguage, videoType);

  // 计算边距
  const marginV =
    Math.round(resConfig.marginBase * typeConfig.marginScale) +
    (videoType === "vertical" ? langConfig.verticalExtra : langConfig.marginExtra);

  // 打印详细的视频匹配信息
  console.log(`
=== 视频字幕样式匹配详情 ===
📹 视频信息:
  - 分辨率: ${videoWidth || "未知"} × ${videoHeight || "未知"}
  - 匹配维度: ${Math.min(videoWidth || 0, videoHeight || 0)}px (较小维度)
  - 分辨率等级: ${resolutionLevel}
  - 视频类型: ${videoType} ${
    videoType === "horizontal" ? "(横屏)" : videoType === "vertical" ? "(竖屏)" : "(正方形)"
  }

🎨 字幕样式配置:
  - 智能计算字体大小: ${fontSize}px
  - 目标语言: ${targetLanguage}
  - 字体名称: ${languageFont.split(",")[0]}

📐 边距配置:
  - 基础底部边距: ${resConfig.marginBase}px
  - 边距缩放比例: ${typeConfig.marginScale}
  - 语言调整: +${videoType === "vertical" ? langConfig.verticalExtra : langConfig.marginExtra}px
  - 最终底部边距: ${marginV}px
  - 左右边距: ${typeConfig.sideMargin}px

🎯 匹配规则:
  - 使用智能字体大小计算算法
  - ${videoType}视频使用${typeConfig.fontScale}倍字体缩放
  - ${targetLanguage}语言额外调整
========================
  `);

  // 获取基础样式
  const baseStyle = assStyleConfig.baseStyle || {};

  // 返回完整样式
  return {
    ...baseStyle,
    fontName: languageFont, // 使用语言特定字体
    fontSize: fontSize,
    marginL: typeConfig.sideMargin,
    marginR: typeConfig.sideMargin,
    marginV: marginV,
  };
}

async function uploadTranslatedAssToOSS(taskId, assContent) {
  const OSS = require("ali-oss");

  const aliyunConfig = createConfig({
    pluginId: "aliyun-oss",
    defaultConfig: {
      region: "oss-cn-shanghai",
      bucket: "video--tanslate",
    },
  });

  const accessKeyId = aliyunConfig.config("accessKeyId");
  const accessKeySecret = aliyunConfig.config("accessKeySecret");
  const region = aliyunConfig.config("region");
  const bucketName = aliyunConfig.config("bucket");

  if (!accessKeyId || !accessKeySecret) {
    throw new Error("阿里云OSS配置缺失");
  }

  const client = new OSS({
    accessKeyId,
    accessKeySecret,
    bucket: bucketName,
    region,
  });

  const timestamp = Date.now();
  const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.ass`;

  const uploadResult = await client.put(objectKey, Buffer.from(assContent, "utf8"), {
    headers: {
      "Content-Type": "text/plain; charset=utf-8",
    },
  });

  return {
    subtitleOssUrl: uploadResult.url,
    objectKey,
  };
}



exports.main = async (event) => {
  const startTime = Date.now();

  try {
    const { taskId, sourceLanguage = "auto", targetLanguage = "zh" } = event;

    // 标准化目标语言标识符
    const normalizedTargetLanguage = normalizeLanguageIdentifier(targetLanguage);

    console.log("subtitle-translation-gpt 云函数启动（纯并发处理版本）");
    console.log("输入参数：", { taskId, sourceLanguage, targetLanguage: normalizedTargetLanguage });
    if (targetLanguage !== normalizedTargetLanguage) {
      console.log(`目标语言标准化: ${targetLanguage} -> ${normalizedTargetLanguage}`);
    }

    if (!taskId) {
      return createErrorResponse(400, "缺少必要参数：taskId");
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证和获取任务信息
    const { task, actualSourceLanguage } = await validateAndGetTask(
      tasksCollection,
      taskId,
      sourceLanguage
    );

    // 检查是否需要翻译
    if (actualSourceLanguage === normalizedTargetLanguage) {
      console.log(
        `源语言(${actualSourceLanguage})和目标语言(${normalizedTargetLanguage})相同，跳过翻译但继续处理字幕文件`
      );

      // 即使语言相同，也需要完成完整流程：下载、解析、生成ASS、上传、更新状态
      console.log("开始下载字幕文件...");
      const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

      console.log("解析SRT字幕文件...");
      const subtitleEntries = parseSRT(srtContent);
      if (subtitleEntries.length === 0) {
        throw new Error("字幕文件为空或格式错误");
      }
      console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

      // 生成和上传ASS字幕文件（使用原始字幕，无需翻译）
      console.log("生成并上传字幕文件...");

      // 获取视频分辨率信息
      const videoResolution = await getVideoResolution(taskId, tasksCollection);
      const languageSpecificStyle = getLanguageSpecificStyle(
        normalizedTargetLanguage,
        videoResolution.width,
        videoResolution.height
      );
      console.log(
        `使用${normalizedTargetLanguage}语言优化样式: 动态字体${languageSpecificStyle.fontSize}px, 边距${languageSpecificStyle.marginV}px, 分辨率${videoResolution.width}x${videoResolution.height}(${videoResolution.source})`
      );

      const assContent = generateASS(
        subtitleEntries, // 使用原始字幕，无需翻译
        normalizedTargetLanguage,
        languageSpecificStyle,
        videoResolution
      );
      const uploadResult = await uploadTranslatedAssToOSS(taskId, assContent);

      // 更新任务状态为merging，准备字幕烧录
      await tasksCollection.doc(taskId).update({
        status: "merging",
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        translationStarted: false,
        updateTime: new Date(),
      });

      console.log("字幕处理完成，准备启动字幕烧录");

      // 直接启动字幕烧录
      try {
        const mergeResult = await uniCloud.callFunction({
          name: "process-video-task",
          data: { taskId, action: "merge_subtitle" },
        });
        console.log("字幕烧录启动结果：", mergeResult.result);
      } catch (error) {
        console.error("字幕烧录启动异常：", error.message);
      }

      const processingTime = (Date.now() - startTime) / 1000;
      console.log(`字幕处理任务完成，耗时: ${processingTime.toFixed(2)}秒`);

      return createSuccessResponse("语言相同，字幕处理成功", {
        taskId,
        status: "completed",
        reason: "same_language",
        translatedCount: subtitleEntries.length,
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        processingTime,
        sourceLanguage: actualSourceLanguage,
        targetLanguage: normalizedTargetLanguage,
      });
    }

    console.log(`开始翻译流程：${actualSourceLanguage} -> ${normalizedTargetLanguage}`);

    // 获取和验证API配置
    const { apiKey, baseUrl } = await getAndValidateGptConfig();
    const model = CONFIG.DEFAULT_MODEL;

    console.log("GPT配置验证通过", { baseUrl, model, hasApiKey: !!apiKey });

    // 执行翻译流程
    console.log("开始下载字幕文件...");
    const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

    console.log("解析SRT字幕文件...：", srtContent);
    const subtitleEntries = parseSRT(srtContent);
    if (subtitleEntries.length === 0) {
      throw new Error("字幕文件为空或格式错误");
    }
    console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

    // 执行纯并发翻译
    console.log("开始纯并发翻译...");
    const translatedEntries = await translateSubtitlesBatchOptimized(
      subtitleEntries,
      apiKey,
      baseUrl,
      model,
      actualSourceLanguage,
      normalizedTargetLanguage
    );
    console.log("翻译后的字幕：", translatedEntries);
    console.log(`翻译完成，共处理 ${translatedEntries.length} 条字幕`);

    // 生成和上传翻译后的ASS
    console.log("生成并上传翻译后的字幕文件...");

    // 获取视频分辨率信息
    const videoResolution = await getVideoResolution(taskId, tasksCollection);
    const languageSpecificStyle = getLanguageSpecificStyle(
      normalizedTargetLanguage,
      videoResolution.width,
      videoResolution.height
    );
    console.log(
      `使用${normalizedTargetLanguage}语言优化样式: 动态字体${languageSpecificStyle.fontSize}px, 边距${languageSpecificStyle.marginV}px, 亮黄色字体+黑色边框, 分辨率${videoResolution.width}x${videoResolution.height}(${videoResolution.source})`
    );
    const translatedAssContent = generateASS(
      translatedEntries,
      normalizedTargetLanguage,
      languageSpecificStyle,
      videoResolution
    );
    const uploadResult = await uploadTranslatedAssToOSS(taskId, translatedAssContent);

    // 更新任务状态为merging，准备字幕烧录
    await tasksCollection.doc(taskId).update({
      status: "merging",
      subtitleOssUrl: uploadResult.subtitleOssUrl,
      translationStarted: false,
      updateTime: new Date(),
    });

    console.log("翻译完成，准备启动字幕烧录");

    // 直接启动字幕烧录
    try {
      const mergeResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: { taskId, action: "merge_subtitle" },
      });
      console.log("字幕烧录启动结果：", mergeResult.result);
    } catch (error) {
      console.error("字幕烧录启动异常：", error.message);
    }

    const processingTime = (Date.now() - startTime) / 1000;
    console.log(`字幕翻译任务完成，耗时: ${processingTime.toFixed(2)}秒`);

    return createSuccessResponse("字幕翻译成功", {
      taskId,
      status: "completed",
      translatedCount: translatedEntries.length,
      subtitleOssUrl: uploadResult.subtitleOssUrl,
      processingTime,
      sourceLanguage: actualSourceLanguage,
      targetLanguage: normalizedTargetLanguage,
    });
  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    console.error("subtitle-translation-gpt 云函数执行错误：", {
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime.toFixed(2)}秒`,
    });

    return createErrorResponse(500, "字幕翻译失败: " + error.message, {
      processingTime,
    });
  }
};
